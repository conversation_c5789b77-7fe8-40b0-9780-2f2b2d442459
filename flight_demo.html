<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞行视角演示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            font-family: 'Arial', sans-serif;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 10px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 10px;
        }
        
        .crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-radius: 50%;
            pointer-events: none;
            z-index: 50;
        }
        
        .crosshair::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
        }
        
        #attitude {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="info">
            <h3>🛩️ 飞行视角演示</h3>
            <div>体验真实的飞行员视角</div>
            <div>相机始终跟随飞机朝向</div>
        </div>
        
        <div id="controls">
            <div><strong>🎮 飞行控制:</strong></div>
            <div>W - 俯冲 (向下)</div>
            <div>S - 拉升 (向上)</div>
            <div>A - 左转</div>
            <div>D - 右转</div>
            <div>鼠标 - 微调瞄准</div>
            <div>空格 - 加速</div>
        </div>
        
        <div id="attitude">
            <div>飞行姿态:</div>
            <div>俯仰: <span id="pitch">0°</span></div>
            <div>偏航: <span id="yaw">0°</span></div>
            <div>高度: <span id="altitude">0m</span></div>
            <div>速度: <span id="speed">0</span></div>
        </div>
        
        <div class="crosshair"></div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script>
        let scene, camera, renderer;
        let playerAircraft;
        let keys = {};
        let mouse = { x: 0, y: 0 };
        let currentSpeed = 0;

        // 飞行设置
        const FLIGHT_SETTINGS = {
            baseSpeed: 0.3,
            maxSpeed: 0.8,
            acceleration: 0.01,
            deceleration: 0.005,
            rotationSpeed: 0.02,
            mouseRotationSpeed: 0.1
        };

        // 初始化场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x87CEEB, 20, 200);

            // 创建相机 - 设置在飞机后方
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(-8, 3, 0);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x87CEEB);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('container').appendChild(renderer.domElement);

            // 设置光照
            setupLighting();

            // 创建环境
            createEnvironment();

            // 创建飞机
            createPlayerAircraft();

            // 设置事件监听器
            setupEventListeners();

            // 开始渲染循环
            animate();
        }

        // 设置光照
        function setupLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            // 方向光（太阳光）
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 500;
            directionalLight.shadow.camera.left = -100;
            directionalLight.shadow.camera.right = 100;
            directionalLight.shadow.camera.top = 100;
            directionalLight.shadow.camera.bottom = -100;
            scene.add(directionalLight);
        }

        // 创建环境
        function createEnvironment() {
            // 地面
            const groundGeometry = new THREE.PlaneGeometry(500, 500);
            const groundMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x90EE90,
                transparent: true,
                opacity: 0.8
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -20;
            ground.receiveShadow = true;
            scene.add(ground);

            // 添加一些地标建筑
            for (let i = 0; i < 20; i++) {
                const buildingGeometry = new THREE.BoxGeometry(
                    Math.random() * 5 + 2,
                    Math.random() * 20 + 5,
                    Math.random() * 5 + 2
                );
                const buildingMaterial = new THREE.MeshLambertMaterial({ 
                    color: new THREE.Color().setHSL(Math.random() * 0.1 + 0.1, 0.5, 0.5)
                });
                const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
                
                building.position.set(
                    (Math.random() - 0.5) * 200,
                    -20 + building.geometry.parameters.height / 2,
                    (Math.random() - 0.5) * 200
                );
                
                building.castShadow = true;
                building.receiveShadow = true;
                scene.add(building);
            }

            // 添加云朵
            for (let i = 0; i < 15; i++) {
                const cloudGeometry = new THREE.SphereGeometry(Math.random() * 3 + 2, 8, 6);
                const cloudMaterial = new THREE.MeshBasicMaterial({ 
                    color: 0xffffff,
                    transparent: true,
                    opacity: 0.6
                });
                const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
                
                cloud.position.set(
                    (Math.random() - 0.5) * 300,
                    Math.random() * 30 + 20,
                    (Math.random() - 0.5) * 300
                );
                
                scene.add(cloud);
            }
        }

        // 创建玩家飞机
        function createPlayerAircraft() {
            const aircraftGroup = new THREE.Group();
            
            // 机身
            const fuselageGeometry = new THREE.ConeGeometry(0.4, 5, 8);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = -Math.PI / 2;
            fuselage.position.x = 0.5;
            fuselage.castShadow = true;
            aircraftGroup.add(fuselage);
            
            // 驾驶舱
            const cockpitGeometry = new THREE.SphereGeometry(0.3, 8, 6);
            const cockpitMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x87CEEB, 
                transparent: true, 
                opacity: 0.7 
            });
            const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
            cockpit.position.set(0.8, 0.1, 0);
            cockpit.scale.set(1, 0.6, 0.8);
            cockpit.castShadow = true;
            aircraftGroup.add(cockpit);
            
            // 机翼
            const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
            const mainWingGeometry = new THREE.BoxGeometry(0.3, 0.1, 4);
            const leftWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
            leftWing.position.set(0, 0, 2);
            leftWing.castShadow = true;
            const rightWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
            rightWing.position.set(0, 0, -2);
            rightWing.castShadow = true;
            aircraftGroup.add(leftWing);
            aircraftGroup.add(rightWing);
            
            // 机翼连接器
            const wingConnectorGeometry = new THREE.BoxGeometry(0.4, 0.15, 4.2);
            const wingConnector = new THREE.Mesh(wingConnectorGeometry, wingMaterial);
            wingConnector.castShadow = true;
            aircraftGroup.add(wingConnector);
            
            // 尾翼
            const verticalTailGeometry = new THREE.BoxGeometry(0.2, 1.5, 0.1);
            const verticalTail = new THREE.Mesh(verticalTailGeometry, wingMaterial);
            verticalTail.position.set(-2, 0.5, 0);
            verticalTail.castShadow = true;
            aircraftGroup.add(verticalTail);
            
            const horizontalTailGeometry = new THREE.BoxGeometry(0.15, 0.1, 1.5);
            const horizontalTail = new THREE.Mesh(horizontalTailGeometry, wingMaterial);
            horizontalTail.position.set(-2, 0, 0);
            horizontalTail.castShadow = true;
            aircraftGroup.add(horizontalTail);
            
            // 螺旋桨
            const propellerGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.2, 8);
            const propellerMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
            const propeller = new THREE.Mesh(propellerGeometry, propellerMaterial);
            propeller.rotation.z = Math.PI / 2;
            propeller.position.set(2.8, 0, 0);
            propeller.castShadow = true;
            aircraftGroup.add(propeller);
            
            // 螺旋桨叶片
            const bladeGeometry = new THREE.BoxGeometry(0.02, 0.8, 0.1);
            const bladeMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const blade1 = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade1.position.set(2.9, 0, 0);
            blade1.castShadow = true;
            const blade2 = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade2.position.set(2.9, 0, 0);
            blade2.rotation.x = Math.PI / 2;
            blade2.castShadow = true;
            aircraftGroup.add(blade1);
            aircraftGroup.add(blade2);
            
            aircraftGroup.position.set(0, 0, 0);
            playerAircraft = aircraftGroup;
            scene.add(playerAircraft);
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 键盘事件
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
                event.preventDefault();
            });

            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
            });

            // 鼠标事件
            document.addEventListener('mousemove', (event) => {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            });

            // 窗口大小调整
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        // 更新飞机
        function updateAircraft() {
            if (!playerAircraft) return;

            const rotationSpeed = FLIGHT_SETTINGS.rotationSpeed;

            // WASD控制飞机的俯仰和偏航
            if (keys['KeyW']) playerAircraft.rotation.x -= rotationSpeed; // 俯冲
            if (keys['KeyS']) playerAircraft.rotation.x += rotationSpeed; // 拉升
            if (keys['KeyA']) playerAircraft.rotation.y += rotationSpeed; // 左转
            if (keys['KeyD']) playerAircraft.rotation.y -= rotationSpeed; // 右转

            // 鼠标微调
            playerAircraft.rotation.y += mouse.x * FLIGHT_SETTINGS.mouseRotationSpeed * 0.01;
            playerAircraft.rotation.x += mouse.y * FLIGHT_SETTINGS.mouseRotationSpeed * 0.01;

            // 限制俯仰角度
            playerAircraft.rotation.x = Math.max(-Math.PI/2.5, Math.min(Math.PI/2.5, playerAircraft.rotation.x));

            // 速度控制
            if (keys['Space']) {
                currentSpeed = Math.min(FLIGHT_SETTINGS.maxSpeed, currentSpeed + FLIGHT_SETTINGS.acceleration);
            } else {
                currentSpeed = Math.max(FLIGHT_SETTINGS.baseSpeed, currentSpeed - FLIGHT_SETTINGS.deceleration);
            }

            // 根据飞机朝向移动
            const direction = new THREE.Vector3(1, 0, 0);
            direction.applyEuler(playerAircraft.rotation);

            const movement = direction.multiplyScalar(currentSpeed);
            playerAircraft.position.add(movement);

            // 更新相机跟随
            updateCamera();

            // 更新UI显示
            updateFlightInfo();
        }

        // 更新相机
        function updateCamera() {
            const cameraDistance = 8;
            const cameraHeight = 3;

            // 计算飞机朝向
            const aircraftDirection = new THREE.Vector3(1, 0, 0);
            aircraftDirection.applyEuler(playerAircraft.rotation);

            // 计算相机位置（飞机后方）
            const cameraOffset = aircraftDirection.clone().multiplyScalar(-cameraDistance);
            const targetCameraPosition = playerAircraft.position.clone().add(cameraOffset);
            targetCameraPosition.y += cameraHeight;

            // 平滑移动相机
            camera.position.lerp(targetCameraPosition, 0.1);

            // 计算瞄准点（飞机前方）
            const lookAtTarget = playerAircraft.position.clone().add(aircraftDirection.multiplyScalar(10));
            camera.lookAt(lookAtTarget);
        }

        // 更新飞行信息显示
        function updateFlightInfo() {
            const pitch = (playerAircraft.rotation.x * 180 / Math.PI).toFixed(1);
            const yaw = (playerAircraft.rotation.y * 180 / Math.PI).toFixed(1);
            const altitude = (playerAircraft.position.y + 20).toFixed(1);
            const speed = (currentSpeed * 100).toFixed(0);

            document.getElementById('pitch').textContent = pitch + '°';
            document.getElementById('yaw').textContent = yaw + '°';
            document.getElementById('altitude').textContent = altitude + 'm';
            document.getElementById('speed').textContent = speed;
        }

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);

            updateAircraft();

            // 旋转螺旋桨
            if (playerAircraft) {
                const propeller = playerAircraft.children.find(child =>
                    child.geometry && child.geometry.type === 'CylinderGeometry'
                );
                if (propeller) {
                    propeller.rotation.x += 0.5;
                }

                // 旋转螺旋桨叶片
                playerAircraft.children.forEach(child => {
                    if (child.geometry && child.geometry.type === 'BoxGeometry' &&
                        child.position.x > 2.8) {
                        child.rotation.x += 0.5;
                    }
                });
            }

            renderer.render(scene, camera);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
