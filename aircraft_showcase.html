<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞机模型展示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            font-family: 'Arial', sans-serif;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="info">
            <h3>🛩️ 飞机模型展示</h3>
            <div>当前展示: <span id="currentModel">玩家战斗机</span></div>
            <div>
                <button onclick="showPlayerAircraft()">玩家战斗机</button>
                <button onclick="showEnemyAircraft()">敌方战斗机</button>
                <button onclick="showBothAircraft()">对比展示</button>
            </div>
        </div>
        
        <div id="controls">
            <div><strong>控制说明:</strong></div>
            <div>🖱️ 鼠标拖拽 - 旋转视角</div>
            <div>🔄 滚轮 - 缩放</div>
            <div>⌨️ 空格键 - 自动旋转</div>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script>
        let scene, camera, renderer, controls;
        let playerAircraft, enemyAircraft;
        let autoRotate = false;
        let currentDisplay = 'player';

        // 初始化场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x87CEEB, 10, 100);

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(8, 3, 8);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x87CEEB);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('container').appendChild(renderer.domElement);

            // 设置光照
            setupLighting();

            // 创建地面
            createGround();

            // 创建飞机模型
            createPlayerAircraft();
            createEnemyAircraft();

            // 设置控制器
            setupControls();

            // 设置事件监听器
            setupEventListeners();

            // 开始渲染循环
            animate();

            // 默认显示玩家飞机
            showPlayerAircraft();
        }

        // 设置光照
        function setupLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            // 方向光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(50, 50, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 200;
            directionalLight.shadow.camera.left = -25;
            directionalLight.shadow.camera.right = 25;
            directionalLight.shadow.camera.top = 25;
            directionalLight.shadow.camera.bottom = -25;
            scene.add(directionalLight);
        }

        // 创建地面
        function createGround() {
            const groundGeometry = new THREE.PlaneGeometry(50, 50);
            const groundMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x90EE90,
                transparent: true,
                opacity: 0.8
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -3;
            ground.receiveShadow = true;
            scene.add(ground);
        }

        // 创建玩家飞机
        function createPlayerAircraft() {
            const aircraftGroup = new THREE.Group();
            
            // 机身 - 流线型设计
            const fuselageGeometry = new THREE.ConeGeometry(0.4, 5, 8);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = -Math.PI / 2;
            fuselage.position.x = 0.5;
            fuselage.castShadow = true;
            aircraftGroup.add(fuselage);
            
            // 驾驶舱
            const cockpitGeometry = new THREE.SphereGeometry(0.3, 8, 6);
            const cockpitMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x87CEEB, 
                transparent: true, 
                opacity: 0.7 
            });
            const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
            cockpit.position.set(0.8, 0.1, 0);
            cockpit.scale.set(1, 0.6, 0.8);
            cockpit.castShadow = true;
            aircraftGroup.add(cockpit);
            
            // 主机翼
            const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
            const mainWingGeometry = new THREE.BoxGeometry(0.3, 0.1, 4);
            const leftWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
            leftWing.position.set(0, 0, 2);
            leftWing.castShadow = true;
            const rightWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
            rightWing.position.set(0, 0, -2);
            rightWing.castShadow = true;
            aircraftGroup.add(leftWing);
            aircraftGroup.add(rightWing);
            
            // 机翼连接部分
            const wingConnectorGeometry = new THREE.BoxGeometry(0.4, 0.15, 4.2);
            const wingConnector = new THREE.Mesh(wingConnectorGeometry, wingMaterial);
            wingConnector.castShadow = true;
            aircraftGroup.add(wingConnector);
            
            // 垂直尾翼
            const verticalTailGeometry = new THREE.BoxGeometry(0.2, 1.5, 0.1);
            const verticalTail = new THREE.Mesh(verticalTailGeometry, wingMaterial);
            verticalTail.position.set(-2, 0.5, 0);
            verticalTail.castShadow = true;
            aircraftGroup.add(verticalTail);
            
            // 水平尾翼
            const horizontalTailGeometry = new THREE.BoxGeometry(0.15, 0.1, 1.5);
            const horizontalTail = new THREE.Mesh(horizontalTailGeometry, wingMaterial);
            horizontalTail.position.set(-2, 0, 0);
            horizontalTail.castShadow = true;
            aircraftGroup.add(horizontalTail);
            
            // 螺旋桨
            const propellerGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.2, 8);
            const propellerMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
            const propeller = new THREE.Mesh(propellerGeometry, propellerMaterial);
            propeller.rotation.z = Math.PI / 2;
            propeller.position.set(2.8, 0, 0);
            propeller.castShadow = true;
            aircraftGroup.add(propeller);
            
            // 螺旋桨叶片
            const bladeGeometry = new THREE.BoxGeometry(0.02, 0.8, 0.1);
            const bladeMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const blade1 = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade1.position.set(2.9, 0, 0);
            blade1.castShadow = true;
            const blade2 = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade2.position.set(2.9, 0, 0);
            blade2.rotation.x = Math.PI / 2;
            blade2.castShadow = true;
            aircraftGroup.add(blade1);
            aircraftGroup.add(blade2);
            
            // 起落架
            const gearGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.3, 6);
            const gearMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const leftGear = new THREE.Mesh(gearGeometry, gearMaterial);
            leftGear.position.set(0.5, -0.4, 0.8);
            leftGear.castShadow = true;
            const rightGear = new THREE.Mesh(gearGeometry, gearMaterial);
            rightGear.position.set(0.5, -0.4, -0.8);
            rightGear.castShadow = true;
            aircraftGroup.add(leftGear);
            aircraftGroup.add(rightGear);
            
            aircraftGroup.position.set(0, 0, 0);
            playerAircraft = aircraftGroup;
            scene.add(playerAircraft);
        }

        // 创建敌机
        function createEnemyAircraft() {
            const enemyGroup = new THREE.Group();

            // 敌机机身 - 现代战斗机设计
            const fuselageGeometry = new THREE.ConeGeometry(0.25, 3.5, 6);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4500 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = -Math.PI / 2;
            fuselage.position.x = 0.3;
            fuselage.castShadow = true;
            enemyGroup.add(fuselage);

            // 敌机驾驶舱
            const cockpitGeometry = new THREE.SphereGeometry(0.2, 6, 4);
            const cockpitMaterial = new THREE.MeshLambertMaterial({
                color: 0x444444,
                transparent: true,
                opacity: 0.8
            });
            const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
            cockpit.position.set(0.5, 0.05, 0);
            cockpit.scale.set(1, 0.5, 0.7);
            cockpit.castShadow = true;
            enemyGroup.add(cockpit);

            // 三角翼设计
            const wingGeometry = new THREE.ConeGeometry(1.5, 2, 3);
            const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });

            const leftWing = new THREE.Mesh(wingGeometry, wingMaterial);
            leftWing.rotation.z = Math.PI / 2;
            leftWing.rotation.y = Math.PI / 6;
            leftWing.position.set(-0.5, 0, 1.2);
            leftWing.castShadow = true;

            const rightWing = new THREE.Mesh(wingGeometry, wingMaterial);
            rightWing.rotation.z = -Math.PI / 2;
            rightWing.rotation.y = -Math.PI / 6;
            rightWing.position.set(-0.5, 0, -1.2);
            rightWing.castShadow = true;

            enemyGroup.add(leftWing);
            enemyGroup.add(rightWing);

            // 垂直尾翼
            const tailGeometry = new THREE.BoxGeometry(0.15, 1, 0.08);
            const tail = new THREE.Mesh(tailGeometry, wingMaterial);
            tail.position.set(-1.5, 0.3, 0);
            tail.castShadow = true;
            enemyGroup.add(tail);

            // 喷气引擎
            const engineGeometry = new THREE.CylinderGeometry(0.15, 0.12, 0.8, 8);
            const engineMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const engine = new THREE.Mesh(engineGeometry, engineMaterial);
            engine.rotation.z = Math.PI / 2;
            engine.position.set(-1.8, 0, 0);
            engine.castShadow = true;
            enemyGroup.add(engine);

            enemyGroup.position.set(0, 0, 0);
            enemyAircraft = enemyGroup;
            scene.add(enemyAircraft);
        }

        // 简单的鼠标控制
        function setupControls() {
            let mouseDown = false;
            let mouseX = 0;
            let mouseY = 0;

            renderer.domElement.addEventListener('mousedown', (event) => {
                mouseDown = true;
                mouseX = event.clientX;
                mouseY = event.clientY;
            });

            renderer.domElement.addEventListener('mouseup', () => {
                mouseDown = false;
            });

            renderer.domElement.addEventListener('mousemove', (event) => {
                if (!mouseDown) return;

                const deltaX = event.clientX - mouseX;
                const deltaY = event.clientY - mouseY;

                camera.position.x = camera.position.x * Math.cos(deltaX * 0.01) - camera.position.z * Math.sin(deltaX * 0.01);
                camera.position.z = camera.position.x * Math.sin(deltaX * 0.01) + camera.position.z * Math.cos(deltaX * 0.01);
                camera.position.y += deltaY * 0.01;

                camera.lookAt(0, 0, 0);

                mouseX = event.clientX;
                mouseY = event.clientY;
            });

            renderer.domElement.addEventListener('wheel', (event) => {
                const scale = event.deltaY > 0 ? 1.1 : 0.9;
                camera.position.multiplyScalar(scale);
                camera.lookAt(0, 0, 0);
            });
        }

        // 设置事件监听器
        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                if (event.code === 'Space') {
                    event.preventDefault();
                    autoRotate = !autoRotate;
                }
            });

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        // 显示玩家飞机
        function showPlayerAircraft() {
            playerAircraft.visible = true;
            enemyAircraft.visible = false;
            currentDisplay = 'player';
            document.getElementById('currentModel').textContent = '玩家战斗机 (蓝色螺旋桨战斗机)';
        }

        // 显示敌机
        function showEnemyAircraft() {
            playerAircraft.visible = false;
            enemyAircraft.visible = true;
            currentDisplay = 'enemy';
            document.getElementById('currentModel').textContent = '敌方战斗机 (红色喷气式战斗机)';
        }

        // 对比展示
        function showBothAircraft() {
            playerAircraft.visible = true;
            enemyAircraft.visible = true;
            playerAircraft.position.set(-3, 0, 0);
            enemyAircraft.position.set(3, 0, 0);
            currentDisplay = 'both';
            document.getElementById('currentModel').textContent = '对比展示 (蓝色 vs 红色)';
        }

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);

            // 自动旋转
            if (autoRotate) {
                if (currentDisplay === 'player') {
                    playerAircraft.rotation.y += 0.01;
                } else if (currentDisplay === 'enemy') {
                    enemyAircraft.rotation.y += 0.01;
                } else {
                    playerAircraft.rotation.y += 0.01;
                    enemyAircraft.rotation.y += 0.01;
                }
            }

            renderer.render(scene, camera);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
