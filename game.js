// 游戏状态
let scene, camera, renderer, controls;
let playerAircraft, enemies = [], bullets = [], enemyBullets = [];
let gameState = {
    score: 0,
    lives: 3,
    ammo: 30,
    maxAmmo: 30,
    isGameOver: false,
    isPlaying: false
};

// 输入控制
let keys = {};
let mouse = { x: 0, y: 0 };

// 游戏设置
const GAME_SETTINGS = {
    playerSpeed: 0.3,
    bulletSpeed: 1.0,
    enemySpeed: 0.1,
    enemySpawnRate: 0.02,
    bulletLifetime: 100,
    worldSize: 50
};

// 初始化游戏
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x87CEEB, 10, 100);

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(-8, 3, 0); // 设置在飞机后方，与飞机朝向一致

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x87CEEB);

    // 检查是否支持阴影
    if (renderer.capabilities.maxTextureSize >= 1024) {
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }

    document.getElementById('gameContainer').appendChild(renderer.domElement);

    // 创建光照
    setupLighting();

    // 创建地面
    createGround();

    // 创建玩家飞机
    createPlayerAircraft();

    // 设置事件监听器
    setupEventListeners();

    // 开始游戏循环
    animate();
}

// 设置光照
function setupLighting() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // 方向光（太阳光）
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(50, 50, 50);

    // 只在支持阴影的情况下启用阴影
    if (renderer && renderer.shadowMap.enabled) {
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -25;
        directionalLight.shadow.camera.right = 25;
        directionalLight.shadow.camera.top = 25;
        directionalLight.shadow.camera.bottom = -25;
    }

    scene.add(directionalLight);
}

// 创建地面
function createGround() {
    const groundGeometry = new THREE.PlaneGeometry(200, 200);
    const groundMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x90EE90,
        transparent: true,
        opacity: 0.8
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.position.y = -10;
    ground.receiveShadow = true;
    scene.add(ground);

    // 添加一些云朵
    createClouds();
}

// 创建云朵
function createClouds() {
    for (let i = 0; i < 20; i++) {
        const cloudGeometry = new THREE.SphereGeometry(Math.random() * 3 + 2, 8, 6);
        const cloudMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xffffff,
            transparent: true,
            opacity: 0.7
        });
        const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
        
        cloud.position.set(
            (Math.random() - 0.5) * 200,
            Math.random() * 20 + 10,
            (Math.random() - 0.5) * 200
        );
        
        cloud.scale.set(
            Math.random() * 2 + 1,
            Math.random() * 0.5 + 0.5,
            Math.random() * 2 + 1
        );
        
        scene.add(cloud);
    }
}

// 创建玩家飞机
function createPlayerAircraft() {
    const aircraftGroup = new THREE.Group();

    try {
        // 机身 - 流线型战斗机设计
        const fuselageGeometry = new THREE.ConeGeometry(0.4, 5, 8);
        const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = -Math.PI / 2; // 尖头朝前
        fuselage.position.x = 0.5;

        if (renderer && renderer.shadowMap.enabled) {
            fuselage.castShadow = true;
        }
        aircraftGroup.add(fuselage);

        // 驾驶舱
        const cockpitGeometry = new THREE.SphereGeometry(0.3, 8, 6);
        const cockpitMaterial = new THREE.MeshLambertMaterial({
            color: 0x87CEEB,
            transparent: true,
            opacity: 0.7
        });
        const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
        cockpit.position.set(0.8, 0.1, 0);
        cockpit.scale.set(1, 0.6, 0.8);
        aircraftGroup.add(cockpit);

        // 主机翼
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
        const mainWingGeometry = new THREE.BoxGeometry(0.3, 0.1, 4);
        const leftWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
        leftWing.position.set(0, 0, 2);
        const rightWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
        rightWing.position.set(0, 0, -2);

        if (renderer && renderer.shadowMap.enabled) {
            leftWing.castShadow = true;
            rightWing.castShadow = true;
        }
        aircraftGroup.add(leftWing);
        aircraftGroup.add(rightWing);

        // 机翼连接部分
        const wingConnectorGeometry = new THREE.BoxGeometry(0.4, 0.15, 4.2);
        const wingConnector = new THREE.Mesh(wingConnectorGeometry, wingMaterial);
        if (renderer && renderer.shadowMap.enabled) {
            wingConnector.castShadow = true;
        }
        aircraftGroup.add(wingConnector);

        // 垂直尾翼
        const verticalTailGeometry = new THREE.BoxGeometry(0.2, 1.5, 0.1);
        const verticalTail = new THREE.Mesh(verticalTailGeometry, wingMaterial);
        verticalTail.position.set(-2, 0.5, 0);
        if (renderer && renderer.shadowMap.enabled) {
            verticalTail.castShadow = true;
        }
        aircraftGroup.add(verticalTail);

        // 水平尾翼
        const horizontalTailGeometry = new THREE.BoxGeometry(0.15, 0.1, 1.5);
        const horizontalTail = new THREE.Mesh(horizontalTailGeometry, wingMaterial);
        horizontalTail.position.set(-2, 0, 0);
        if (renderer && renderer.shadowMap.enabled) {
            horizontalTail.castShadow = true;
        }
        aircraftGroup.add(horizontalTail);

        // 螺旋桨
        const propellerGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.2, 8);
        const propellerMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const propeller = new THREE.Mesh(propellerGeometry, propellerMaterial);
        propeller.rotation.z = Math.PI / 2;
        propeller.position.set(2.8, 0, 0);
        aircraftGroup.add(propeller);

        // 螺旋桨叶片
        const bladeGeometry = new THREE.BoxGeometry(0.02, 0.8, 0.1);
        const bladeMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const blade1 = new THREE.Mesh(bladeGeometry, bladeMaterial);
        blade1.position.set(2.9, 0, 0);
        const blade2 = new THREE.Mesh(bladeGeometry, bladeMaterial);
        blade2.position.set(2.9, 0, 0);
        blade2.rotation.x = Math.PI / 2;
        aircraftGroup.add(blade1);
        aircraftGroup.add(blade2);

        aircraftGroup.position.set(0, 0, 0);
        playerAircraft = aircraftGroup;
        scene.add(playerAircraft);

        console.log('玩家飞机创建成功');
    } catch (error) {
        console.error('创建玩家飞机时出错:', error);
        // 创建简化版本
        const simpleGeometry = new THREE.BoxGeometry(2, 0.5, 4);
        const simpleMaterial = new THREE.MeshBasicMaterial({ color: 0x4169E1 });
        const simpleMesh = new THREE.Mesh(simpleGeometry, simpleMaterial);
        aircraftGroup.add(simpleMesh);
        playerAircraft = aircraftGroup;
        scene.add(playerAircraft);
    }
}

// 创建敌机
function createEnemyAircraft() {
    const enemyGroup = new THREE.Group();

    try {
        // 敌机机身 - 现代战斗机设计
        const fuselageGeometry = new THREE.ConeGeometry(0.25, 3.5, 6);
        const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4500 });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = -Math.PI / 2; // 尖头朝前
        fuselage.position.x = 0.3;

        if (renderer && renderer.shadowMap.enabled) {
            fuselage.castShadow = true;
        }
        enemyGroup.add(fuselage);

        // 敌机驾驶舱
        const cockpitGeometry = new THREE.SphereGeometry(0.2, 6, 4);
        const cockpitMaterial = new THREE.MeshLambertMaterial({
            color: 0x444444,
            transparent: true,
            opacity: 0.8
        });
        const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
        cockpit.position.set(0.5, 0.05, 0);
        cockpit.scale.set(1, 0.5, 0.7);
        enemyGroup.add(cockpit);

        // 三角翼设计（现代战斗机风格）
        const wingGeometry = new THREE.ConeGeometry(1.5, 2, 3);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });

        const leftWing = new THREE.Mesh(wingGeometry, wingMaterial);
        leftWing.rotation.z = Math.PI / 2;
        leftWing.rotation.y = Math.PI / 6;
        leftWing.position.set(-0.5, 0, 1.2);

        const rightWing = new THREE.Mesh(wingGeometry, wingMaterial);
        rightWing.rotation.z = -Math.PI / 2;
        rightWing.rotation.y = -Math.PI / 6;
        rightWing.position.set(-0.5, 0, -1.2);

        if (renderer && renderer.shadowMap.enabled) {
            leftWing.castShadow = true;
            rightWing.castShadow = true;
        }
        enemyGroup.add(leftWing);
        enemyGroup.add(rightWing);

        // 垂直尾翼
        const tailGeometry = new THREE.BoxGeometry(0.15, 1, 0.08);
        const tail = new THREE.Mesh(tailGeometry, wingMaterial);
        tail.position.set(-1.5, 0.3, 0);
        if (renderer && renderer.shadowMap.enabled) {
            tail.castShadow = true;
        }
        enemyGroup.add(tail);

        // 喷气引擎
        const engineGeometry = new THREE.CylinderGeometry(0.15, 0.12, 0.8, 8);
        const engineMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const engine = new THREE.Mesh(engineGeometry, engineMaterial);
        engine.rotation.z = Math.PI / 2;
        engine.position.set(-1.8, 0, 0);
        if (renderer && renderer.shadowMap.enabled) {
            engine.castShadow = true;
        }
        enemyGroup.add(engine);
    } catch (error) {
        console.error('创建敌机时出错:', error);
        // 创建简化版本
        const simpleGeometry = new THREE.BoxGeometry(1.5, 0.3, 3);
        const simpleMaterial = new THREE.MeshBasicMaterial({ color: 0xFF4500 });
        const simpleMesh = new THREE.Mesh(simpleGeometry, simpleMaterial);
        enemyGroup.add(simpleMesh);
    }
    
    // 随机生成位置
    enemyGroup.position.set(
        (Math.random() - 0.5) * GAME_SETTINGS.worldSize,
        Math.random() * 10 + 5,
        -GAME_SETTINGS.worldSize / 2
    );
    
    // 添加敌机属性
    enemyGroup.userData = {
        health: 1,
        lastShot: 0,
        shootInterval: 60 + Math.random() * 120 // 1-3秒射击间隔
    };
    
    enemies.push(enemyGroup);
    scene.add(enemyGroup);
}

// 创建子弹
function createBullet(position, direction, isPlayerBullet = true) {
    const bulletGeometry = new THREE.SphereGeometry(0.1, 8, 6);
    const bulletMaterial = new THREE.MeshLambertMaterial({ 
        color: isPlayerBullet ? 0xFFFF00 : 0xFF0000,
        emissive: isPlayerBullet ? 0x444400 : 0x440000
    });
    const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
    
    bullet.position.copy(position);
    bullet.userData = {
        direction: direction.clone(),
        lifetime: GAME_SETTINGS.bulletLifetime,
        isPlayerBullet: isPlayerBullet
    };
    
    if (isPlayerBullet) {
        bullets.push(bullet);
    } else {
        enemyBullets.push(bullet);
    }
    
    scene.add(bullet);
}

// 设置事件监听器
function setupEventListeners() {
    // 键盘事件
    document.addEventListener('keydown', (event) => {
        keys[event.code] = true;

        // 射击
        if (event.code === 'Space') {
            event.preventDefault();
            shoot();
        }

        // 重新装弹
        if (event.code === 'KeyR') {
            reload();
        }
    });

    document.addEventListener('keyup', (event) => {
        keys[event.code] = false;
    });

    // 鼠标事件
    document.addEventListener('mousemove', (event) => {
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
    });

    document.addEventListener('mousedown', (event) => {
        if (event.button === 0) { // 左键
            shoot();
        }
    });

    // 窗口大小调整
    window.addEventListener('resize', onWindowResize);
}

// 窗口大小调整
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 射击
function shoot() {
    if (!gameState.isPlaying || gameState.ammo <= 0) return;

    gameState.ammo--;
    updateUI();

    // 从飞机前方发射子弹
    const bulletPosition = playerAircraft.position.clone();
    bulletPosition.add(new THREE.Vector3(2, 0, 0));

    // 计算射击方向（朝向鼠标位置）
    const raycaster = new THREE.Raycaster();
    raycaster.setFromCamera(mouse, camera);

    const direction = raycaster.ray.direction.clone();
    createBullet(bulletPosition, direction, true);
}

// 重新装弹
function reload() {
    if (!gameState.isPlaying) return;
    gameState.ammo = gameState.maxAmmo;
    updateUI();
}

// 更新玩家飞机
function updatePlayer() {
    if (!playerAircraft || !gameState.isPlaying) return;

    const speed = GAME_SETTINGS.playerSpeed;
    const rotationSpeed = 0.02;

    // WASD控制飞机的俯仰和偏航 (保持机翼水平)
    if (keys['KeyW']) playerAircraft.rotation.z += rotationSpeed; // 拉升 (机头向上，机翼保持水平)
    if (keys['KeyS']) playerAircraft.rotation.z -= rotationSpeed; // 俯冲 (机头向下，机翼保持水平)
    if (keys['KeyA']) playerAircraft.rotation.y += rotationSpeed; // 左转
    if (keys['KeyD']) playerAircraft.rotation.y -= rotationSpeed; // 右转

    // 限制俯仰角度，避免过度翻转 (现在使用Z轴)
    playerAircraft.rotation.z = Math.max(-Math.PI/3, Math.min(Math.PI/3, playerAircraft.rotation.z));

    // 根据飞机朝向移动（始终向前飞行）
    const direction = new THREE.Vector3(1, 0, 0); // 飞机头部朝向
    direction.applyEuler(playerAircraft.rotation);

    const movement = direction.multiplyScalar(speed);
    playerAircraft.position.add(movement);

    // 限制飞机在游戏区域内
    const limit = GAME_SETTINGS.worldSize / 2;
    playerAircraft.position.x = Math.max(-limit, Math.min(limit, playerAircraft.position.x));
    playerAircraft.position.z = Math.max(-limit, Math.min(limit, playerAircraft.position.z));

    // 根据鼠标位置旋转飞机
    const targetRotationY = mouse.x * 0.3;
    const targetRotationZ = -mouse.y * 0.2;

    playerAircraft.rotation.y += (targetRotationY - playerAircraft.rotation.y) * 0.1;
    playerAircraft.rotation.z += (targetRotationZ - playerAircraft.rotation.z) * 0.1;

    // 更新相机跟随 - 始终保持在飞机后方
    const cameraDistance = 8; // 相机距离飞机的距离
    const cameraHeight = 3;   // 相机高度偏移

    // 计算飞机的朝向向量（飞机头部朝向X轴正方向）
    const aircraftDirection = new THREE.Vector3(1, 0, 0);
    aircraftDirection.applyEuler(playerAircraft.rotation);

    // 计算相机应该在的位置（飞机后方）
    const cameraOffset = aircraftDirection.clone().multiplyScalar(-cameraDistance);
    const targetCameraPosition = playerAircraft.position.clone().add(cameraOffset);
    targetCameraPosition.y += cameraHeight;

    // 平滑移动相机到目标位置
    camera.position.lerp(targetCameraPosition, 0.1);

    // 计算飞机前方的目标点
    const lookAtTarget = playerAircraft.position.clone().add(aircraftDirection.multiplyScalar(5));
    camera.lookAt(lookAtTarget);
}

// 更新敌机
function updateEnemies() {
    if (!gameState.isPlaying) return;

    // 生成新敌机
    if (Math.random() < GAME_SETTINGS.enemySpawnRate) {
        createEnemyAircraft();
    }

    // 更新现有敌机
    for (let i = enemies.length - 1; i >= 0; i--) {
        const enemy = enemies[i];

        // 移动敌机
        enemy.position.z += GAME_SETTINGS.enemySpeed;

        // 敌机AI射击
        enemy.userData.lastShot++;
        if (enemy.userData.lastShot > enemy.userData.shootInterval) {
            enemyShoot(enemy);
            enemy.userData.lastShot = 0;
        }

        // 移除超出边界的敌机
        if (enemy.position.z > GAME_SETTINGS.worldSize / 2) {
            scene.remove(enemy);
            enemies.splice(i, 1);
        }
    }
}

// 敌机射击
function enemyShoot(enemy) {
    if (!playerAircraft) return;

    const bulletPosition = enemy.position.clone();
    const direction = new THREE.Vector3();
    direction.subVectors(playerAircraft.position, enemy.position).normalize();

    createBullet(bulletPosition, direction, false);
}

// 更新子弹
function updateBullets() {
    // 更新玩家子弹
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];

        // 移动子弹
        bullet.position.add(bullet.userData.direction.clone().multiplyScalar(GAME_SETTINGS.bulletSpeed));

        // 减少生命周期
        bullet.userData.lifetime--;

        // 移除过期子弹
        if (bullet.userData.lifetime <= 0) {
            scene.remove(bullet);
            bullets.splice(i, 1);
            continue;
        }

        // 检查与敌机的碰撞
        for (let j = enemies.length - 1; j >= 0; j--) {
            const enemy = enemies[j];
            const distance = bullet.position.distanceTo(enemy.position);

            if (distance < 1.5) {
                // 击中敌机
                scene.remove(bullet);
                scene.remove(enemy);
                bullets.splice(i, 1);
                enemies.splice(j, 1);

                // 增加分数
                gameState.score += 100;
                updateUI();

                // 创建爆炸效果
                createExplosion(enemy.position);
                break;
            }
        }
    }

    // 更新敌机子弹
    for (let i = enemyBullets.length - 1; i >= 0; i--) {
        const bullet = enemyBullets[i];

        // 移动子弹
        bullet.position.add(bullet.userData.direction.clone().multiplyScalar(GAME_SETTINGS.bulletSpeed * 0.7));

        // 减少生命周期
        bullet.userData.lifetime--;

        // 移除过期子弹
        if (bullet.userData.lifetime <= 0) {
            scene.remove(bullet);
            enemyBullets.splice(i, 1);
            continue;
        }

        // 检查与玩家的碰撞
        if (playerAircraft) {
            const distance = bullet.position.distanceTo(playerAircraft.position);

            if (distance < 2) {
                // 玩家被击中
                scene.remove(bullet);
                enemyBullets.splice(i, 1);

                // 减少生命
                gameState.lives--;
                updateUI();

                // 创建爆炸效果
                createExplosion(playerAircraft.position);

                // 检查游戏结束
                if (gameState.lives <= 0) {
                    gameOver();
                }
                break;
            }
        }
    }
}

// 创建爆炸效果
function createExplosion(position) {
    const explosionGroup = new THREE.Group();

    // 创建多个粒子
    for (let i = 0; i < 10; i++) {
        const particleGeometry = new THREE.SphereGeometry(0.1, 4, 4);
        const particleMaterial = new THREE.MeshBasicMaterial({
            color: new THREE.Color().setHSL(Math.random() * 0.1, 1, 0.5),
            transparent: true,
            opacity: 0.8
        });
        const particle = new THREE.Mesh(particleGeometry, particleMaterial);

        particle.position.copy(position);
        particle.userData = {
            velocity: new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ),
            life: 30
        };

        explosionGroup.add(particle);
    }

    scene.add(explosionGroup);

    // 动画爆炸粒子
    const animateExplosion = () => {
        let activeParticles = 0;

        explosionGroup.children.forEach(particle => {
            if (particle.userData.life > 0) {
                particle.position.add(particle.userData.velocity);
                particle.userData.velocity.multiplyScalar(0.95);
                particle.userData.life--;
                particle.material.opacity = particle.userData.life / 30;
                activeParticles++;
            }
        });

        if (activeParticles > 0) {
            requestAnimationFrame(animateExplosion);
        } else {
            scene.remove(explosionGroup);
        }
    };

    animateExplosion();
}

// 更新UI
function updateUI() {
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('lives').textContent = gameState.lives;
    document.getElementById('ammo').textContent = gameState.ammo;
}

// 开始游戏
function startGame() {
    gameState = {
        score: 0,
        lives: 3,
        ammo: 30,
        maxAmmo: 30,
        isGameOver: false,
        isPlaying: true
    };

    // 清理场景
    enemies.forEach(enemy => scene.remove(enemy));
    bullets.forEach(bullet => scene.remove(bullet));
    enemyBullets.forEach(bullet => scene.remove(bullet));

    enemies.length = 0;
    bullets.length = 0;
    enemyBullets.length = 0;

    // 重置玩家位置
    if (playerAircraft) {
        playerAircraft.position.set(0, 0, 0);
        playerAircraft.rotation.set(0, 0, 0);
    }

    // 隐藏游戏结束界面
    document.getElementById('gameOver').style.display = 'none';

    updateUI();
}

// 游戏结束
function gameOver() {
    gameState.isPlaying = false;
    gameState.isGameOver = true;

    document.getElementById('finalScore').textContent = gameState.score;
    document.getElementById('gameOver').style.display = 'block';
}

// 游戏主循环
function animate() {
    requestAnimationFrame(animate);

    if (gameState.isPlaying) {
        updatePlayer();
        updateEnemies();
        updateBullets();
    }

    renderer.render(scene, camera);
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    console.log('Three.js版本:', THREE.REVISION);

    try {
        init();
        console.log('游戏初始化成功');

        // 显示开始界面
        document.getElementById('gameOver').style.display = 'block';
        document.getElementById('gameOver').querySelector('h2').textContent = '空战游戏';
        document.getElementById('gameOver').querySelector('p').innerHTML = '使用WASD移动飞机<br>鼠标瞄准，左键或空格射击<br>R键重新装弹';
        document.getElementById('finalScore').parentElement.style.display = 'none';
    } catch (error) {
        console.error('游戏初始化失败:', error);
        alert('游戏初始化失败，请检查浏览器控制台');
    }
});
