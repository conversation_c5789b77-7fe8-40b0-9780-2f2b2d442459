<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 空战游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            font-family: 'Arial', sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 200;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }
        
        #startButton {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        
        #startButton:hover {
            background: #45a049;
        }
        
        .crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-radius: 50%;
            pointer-events: none;
            z-index: 50;
        }
        
        .crosshair::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="ui">
            <div>分数: <span id="score">0</span></div>
            <div>生命: <span id="lives">3</span></div>
            <div>弹药: <span id="ammo">30</span></div>
        </div>
        
        <div id="controls">
            <div>控制说明:</div>
            <div>WASD - 移动飞机</div>
            <div>鼠标 - 瞄准</div>
            <div>左键/空格 - 射击</div>
            <div>R - 重新装弹</div>
        </div>
        
        <div class="crosshair"></div>
        
        <div id="gameOver">
            <h2>游戏结束</h2>
            <p>最终分数: <span id="finalScore">0</span></p>
            <button id="startButton" onclick="startGame()">重新开始</button>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script src="game.js"></script>
</body>
</html>
