# Three.js 空战游戏 - 部署说明

## 🎮 游戏完成情况

✅ **已完成的功能**：
- 基于Three.js r160的3D空战游戏
- 完整的飞机控制系统（WASD移动）
- 鼠标瞄准和射击系统
- 敌机AI和自动射击
- 碰撞检测系统
- 爆炸粒子效果
- 分数、生命值、弹药系统
- 游戏开始/结束界面
- 3D光影效果和阴影
- 云朵和地面环境

## 📁 项目文件

```
空战游戏/
├── index.html              # 主游戏页面（完整版）
├── game.js                 # 游戏主逻辑
├── simple_game.html        # 简化测试版本
├── test.html              # Three.js基础测试
├── start_server.py        # Python服务器启动脚本
├── README.md              # 详细说明文档
└── 部署说明.md            # 本文件
```

## 🚀 运行方法

### 方法1: 使用Python服务器（推荐）
```bash
# 在项目目录中运行
python -m http.server 8000

# 或使用提供的启动脚本
python start_server.py
```

### 方法2: 使用Node.js服务器
```bash
# 安装http-server
npm install -g http-server

# 启动服务器
http-server -p 8000
```

### 方法3: 使用VS Code Live Server
1. 安装Live Server扩展
2. 右键点击index.html
3. 选择"Open with Live Server"

## 🌐 访问游戏

启动服务器后，在浏览器中访问：
- **完整游戏**: http://localhost:8000/index.html
- **简化版本**: http://localhost:8000/simple_game.html
- **基础测试**: http://localhost:8000/test.html

## 🎯 游戏特性

### 核心玩法
- **飞机控制**: WASD键控制飞机在3D空间中移动
- **瞄准射击**: 鼠标控制瞄准方向，左键或空格键射击
- **弹药管理**: 有限弹药，按R键重新装弹
- **敌机挑战**: AI敌机会自动生成并射击玩家
- **生存模式**: 3条生命，被击中会减少生命值

### 视觉效果
- **3D环境**: 完整的3D场景，包括天空、地面、云朵
- **实时光影**: 动态光照和阴影效果
- **粒子系统**: 击中目标时的爆炸粒子效果
- **雾化效果**: 远景雾化增强深度感
- **流畅动画**: 60FPS的流畅游戏体验

### 技术实现
- **Three.js r160**: 使用稳定版本的Three.js
- **WebGL渲染**: 硬件加速的3D渲染
- **事件驱动**: 响应式的输入处理系统
- **碰撞检测**: 精确的3D碰撞检测算法
- **内存管理**: 自动清理不需要的游戏对象

## 🔧 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 90+ (推荐)
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Linux
- **内存**: 至少4GB RAM
- **显卡**: 支持WebGL的独立显卡或集成显卡
- **网络**: 需要网络连接加载Three.js库

## 🐛 故障排除

### 常见问题

**1. 游戏无法加载**
- 检查浏览器控制台是否有错误信息
- 确保网络连接正常（需要加载Three.js CDN）
- 尝试刷新页面或清除浏览器缓存

**2. 性能问题**
- 关闭其他占用GPU的程序
- 降低浏览器缩放比例
- 尝试使用Chrome浏览器获得最佳性能

**3. 控制不响应**
- 确保游戏窗口处于焦点状态
- 检查键盘和鼠标是否正常工作
- 尝试点击游戏区域后再操作

**4. Three.js加载失败**
- 检查网络连接
- 尝试访问test.html测试Three.js是否正常加载
- 如果CDN不可用，可以下载Three.js到本地

### 调试模式
在浏览器控制台中可以看到：
- Three.js版本信息
- 游戏初始化状态
- 错误信息（如果有）

## 🎨 自定义和扩展

### 修改游戏参数
在game.js中的GAME_SETTINGS对象可以调整：
```javascript
const GAME_SETTINGS = {
    playerSpeed: 0.3,      // 玩家飞机速度
    bulletSpeed: 1.0,      // 子弹速度
    enemySpeed: 0.1,       // 敌机速度
    enemySpawnRate: 0.02,  // 敌机生成频率
    bulletLifetime: 100,   // 子弹生存时间
    worldSize: 50          // 游戏世界大小
};
```

### 添加新功能
- 更多武器类型
- 不同的敌机模型
- 道具系统
- 关卡设计
- 音效和背景音乐
- 多人游戏模式

## 📊 性能优化建议

1. **对象池**: 重用子弹和敌机对象而不是频繁创建/销毁
2. **LOD系统**: 根据距离调整模型细节
3. **视锥剔除**: 只渲染视野内的对象
4. **纹理优化**: 使用压缩纹理格式
5. **批量渲染**: 合并相似的几何体

## 📝 开发日志

- ✅ 基础3D场景搭建
- ✅ 玩家飞机模型和控制
- ✅ 射击系统实现
- ✅ 敌机AI和生成系统
- ✅ 碰撞检测算法
- ✅ 粒子爆炸效果
- ✅ UI界面和游戏状态管理
- ✅ 光影和视觉效果优化
- ✅ 多版本测试文件
- ✅ 完整文档和部署说明

## 🎉 总结

这是一个功能完整的3D空战游戏，展示了Three.js在游戏开发中的强大能力。游戏包含了现代3D游戏的核心要素：3D渲染、物理碰撞、粒子效果、AI系统等。

代码结构清晰，易于理解和扩展，是学习Three.js游戏开发的优秀示例。

**立即开始游戏**: 运行服务器后访问 http://localhost:8000 即可体验！
