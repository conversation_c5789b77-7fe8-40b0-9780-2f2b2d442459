<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 调试版本</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            font-family: Arial, sans-serif;
        }
        
        #debug {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
            max-width: 400px;
        }
        
        #error {
            color: #ff6666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div id="debug">
        <div>Three.js 调试信息:</div>
        <div id="status">正在加载...</div>
        <div id="error"></div>
    </div>

    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script>
        const debugDiv = document.getElementById('status');
        const errorDiv = document.getElementById('error');
        
        function log(message) {
            console.log(message);
            debugDiv.innerHTML += '<br>' + message;
        }
        
        function logError(message, error) {
            console.error(message, error);
            errorDiv.innerHTML += '<br>❌ ' + message + ': ' + error.message;
        }
        
        try {
            log('Three.js版本: ' + THREE.REVISION);
            log('开始初始化...');
            
            // 创建场景
            const scene = new THREE.Scene();
            log('✅ 场景创建成功');
            
            // 创建相机
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 5, 10);
            log('✅ 相机创建成功');
            
            // 创建渲染器
            const renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x87CEEB);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);
            log('✅ 渲染器创建成功');
            
            // 创建光照
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            log('✅ 环境光创建成功');
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(50, 50, 50);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            log('✅ 方向光创建成功');
            
            // 创建地面
            const groundGeometry = new THREE.PlaneGeometry(200, 200);
            const groundMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x90EE90,
                transparent: true,
                opacity: 0.8
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -10;
            ground.receiveShadow = true;
            scene.add(ground);
            log('✅ 地面创建成功');
            
            // 创建玩家飞机
            log('开始创建玩家飞机...');
            const aircraftGroup = new THREE.Group();
            
            // 机身
            log('创建机身...');
            const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = Math.PI / 2;
            fuselage.castShadow = true;
            aircraftGroup.add(fuselage);
            log('✅ 机身创建成功');
            
            // 机翼
            log('创建机翼...');
            const wingGeometry = new THREE.BoxGeometry(6, 0.2, 1.5);
            const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
            const wings = new THREE.Mesh(wingGeometry, wingMaterial);
            wings.castShadow = true;
            aircraftGroup.add(wings);
            log('✅ 机翼创建成功');
            
            // 尾翼
            log('创建尾翼...');
            const tailGeometry = new THREE.BoxGeometry(0.5, 2, 0.2);
            const tailMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
            const tail = new THREE.Mesh(tailGeometry, tailMaterial);
            tail.position.set(-1.5, 0, 0);
            tail.castShadow = true;
            aircraftGroup.add(tail);
            log('✅ 尾翼创建成功');
            
            aircraftGroup.position.set(0, 0, 0);
            scene.add(aircraftGroup);
            log('✅ 玩家飞机添加到场景成功');
            
            // 创建一个简单的敌机测试
            log('创建测试敌机...');
            const enemyGroup = new THREE.Group();
            const enemyGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3, 6);
            const enemyMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4500 });
            const enemyFuselage = new THREE.Mesh(enemyGeometry, enemyMaterial);
            enemyFuselage.rotation.z = Math.PI / 2;
            enemyGroup.add(enemyFuselage);
            enemyGroup.position.set(5, 2, -5);
            scene.add(enemyGroup);
            log('✅ 测试敌机创建成功');
            
            // 开始渲染
            log('开始渲染循环...');
            function animate() {
                requestAnimationFrame(animate);
                
                // 旋转飞机以便观察
                aircraftGroup.rotation.y += 0.01;
                enemyGroup.rotation.y += 0.02;
                
                renderer.render(scene, camera);
            }
            
            animate();
            log('✅ 游戏初始化完成！');
            
        } catch (error) {
            logError('初始化失败', error);
        }
        
        // 监听全局错误
        window.addEventListener('error', (event) => {
            logError('运行时错误', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            logError('Promise错误', event.reason);
        });
    </script>
</body>
</html>
