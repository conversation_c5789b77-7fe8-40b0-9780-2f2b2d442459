<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版空战游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            font-family: Arial, sans-serif;
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
    </style>
</head>
<body>
    <div id="ui">
        <div>分数: <span id="score">0</span></div>
        <div>状态: <span id="status">加载中...</span></div>
    </div>
    
    <div id="controls">
        <div>控制说明:</div>
        <div>WASD - 移动飞机</div>
        <div>空格 - 射击</div>
    </div>

    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script>
        let scene, camera, renderer;
        let playerAircraft;
        let score = 0;
        
        // 输入控制
        let keys = {};
        
        function init() {
            // 更新状态
            document.getElementById('status').textContent = '初始化中...';
            
            // 创建场景
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x87CEEB, 10, 100);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 5, 10);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x87CEEB);
            document.body.appendChild(renderer.domElement);
            
            // 创建光照
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(50, 50, 50);
            scene.add(directionalLight);
            
            // 创建地面
            const groundGeometry = new THREE.PlaneGeometry(100, 100);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -5;
            scene.add(ground);
            
            // 创建玩家飞机
            createPlayerAircraft();
            
            // 设置事件监听器
            setupEventListeners();
            
            // 更新状态
            document.getElementById('status').textContent = '游戏运行中';
            
            // 开始游戏循环
            animate();
        }
        
        function createPlayerAircraft() {
            const aircraftGroup = new THREE.Group();
            
            // 机身
            const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = Math.PI / 2;
            aircraftGroup.add(fuselage);
            
            // 机翼
            const wingGeometry = new THREE.BoxGeometry(6, 0.2, 1.5);
            const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
            const wings = new THREE.Mesh(wingGeometry, wingMaterial);
            aircraftGroup.add(wings);
            
            aircraftGroup.position.set(0, 0, 0);
            playerAircraft = aircraftGroup;
            scene.add(playerAircraft);
        }
        
        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
                
                if (event.code === 'Space') {
                    event.preventDefault();
                    shoot();
                }
            });
            
            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
            });
            
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }
        
        function shoot() {
            score += 10;
            document.getElementById('score').textContent = score;
            
            // 创建简单的射击效果
            const bulletGeometry = new THREE.SphereGeometry(0.1, 8, 6);
            const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFF00 });
            const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
            
            bullet.position.copy(playerAircraft.position);
            bullet.position.x += 2;
            scene.add(bullet);
            
            // 子弹动画
            let bulletSpeed = 0.5;
            const moveBullet = () => {
                bullet.position.x += bulletSpeed;
                
                if (bullet.position.x < 50) {
                    requestAnimationFrame(moveBullet);
                } else {
                    scene.remove(bullet);
                }
            };
            moveBullet();
        }
        
        function updatePlayer() {
            if (!playerAircraft) return;
            
            const speed = 0.3;
            
            if (keys['KeyW']) playerAircraft.position.z -= speed;
            if (keys['KeyS']) playerAircraft.position.z += speed;
            if (keys['KeyA']) playerAircraft.position.x -= speed;
            if (keys['KeyD']) playerAircraft.position.x += speed;
            
            // 限制移动范围
            playerAircraft.position.x = Math.max(-20, Math.min(20, playerAircraft.position.x));
            playerAircraft.position.z = Math.max(-20, Math.min(20, playerAircraft.position.z));
            
            // 更新相机
            camera.position.x = playerAircraft.position.x;
            camera.position.z = playerAircraft.position.z + 10;
            camera.lookAt(playerAircraft.position);
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            updatePlayer();
            
            renderer.render(scene, camera);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Three.js版本:', THREE.REVISION);
            init();
        });
    </script>
</body>
</html>
