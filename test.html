<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>Three.js 测试页面</div>
        <div id="status">加载中...</div>
    </div>

    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script>
        // 测试Three.js是否正常加载
        document.getElementById('status').textContent = 'Three.js版本: ' + THREE.REVISION;
        
        // 创建基本场景
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer();
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(renderer.domElement);
        
        // 创建一个旋转的立方体
        const geometry = new THREE.BoxGeometry();
        const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);
        
        camera.position.z = 5;
        
        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            
            cube.rotation.x += 0.01;
            cube.rotation.y += 0.01;
            
            renderer.render(scene, camera);
        }
        
        animate();
        
        // 更新状态
        document.getElementById('status').innerHTML = 
            'Three.js版本: ' + THREE.REVISION + '<br>' +
            '状态: 正常运行<br>' +
            '应该看到一个绿色旋转立方体';
    </script>
</body>
</html>
