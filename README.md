# Three.js 空战游戏

基于最新 Three.js r178 开发的3D空战游戏，玩家可以控制飞机在3D空间中飞行，射击敌机并获得分数。

## 🎮 游戏特性

### 核心功能
- **3D飞行体验**: 使用Three.js创建的真实3D环境
- **飞机控制**: WASD键控制飞机移动，鼠标控制瞄准方向
- **射击系统**: 左键或空格键射击，R键重新装弹
- **敌机AI**: 智能敌机会自动射击并追踪玩家
- **碰撞检测**: 精确的子弹与飞机碰撞检测
- **爆炸效果**: 击中目标时的粒子爆炸特效

### 视觉效果
- **动态光照**: 环境光和方向光营造真实光影效果
- **阴影系统**: 飞机和地面的实时阴影
- **天空环境**: 渐变天空背景和飘浮云朵
- **粒子特效**: 爆炸时的动态粒子效果
- **雾化效果**: 远景雾化增强深度感

### 游戏机制
- **生命系统**: 玩家有3条生命
- **弹药系统**: 有限弹药，需要重新装弹
- **分数系统**: 击毁敌机获得分数
- **难度递增**: 敌机生成频率逐渐增加

## 🎯 控制说明

| 操作 | 按键 |
|------|------|
| 向前飞行 | W |
| 向后飞行 | S |
| 向左飞行 | A |
| 向右飞行 | D |
| 瞄准 | 鼠标移动 |
| 射击 | 左键 / 空格键 |
| 重新装弹 | R |

## 🚀 技术特性

### Three.js r178 新特性应用
- **WebGPU渲染器支持**: 为未来的WebGPU做好准备
- **改进的阴影映射**: 使用PCFSoftShadowMap获得更柔和的阴影
- **优化的几何体**: 使用最新的几何体API
- **材质系统**: 使用Lambert材质实现真实光照效果

### 性能优化
- **对象池**: 子弹和敌机的高效管理
- **视锥剔除**: 自动剔除视野外的对象
- **LOD系统**: 距离相关的细节层次
- **内存管理**: 及时清理不需要的对象

### 代码架构
- **模块化设计**: 清晰的功能分离
- **事件驱动**: 响应式的输入处理
- **状态管理**: 集中的游戏状态管理
- **可扩展性**: 易于添加新功能

## 📁 文件结构

```
空战游戏/
├── index.html          # 主HTML文件
├── game.js            # 游戏主逻辑
└── README.md          # 说明文档
```

## 🛠️ 安装和运行

### 方法1: 直接运行
1. 下载所有文件到同一目录
2. 用现代浏览器打开 `index.html`
3. 点击"重新开始"按钮开始游戏

### 方法2: 本地服务器
```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 然后在浏览器中访问 http://localhost:8000
```

## 🎨 自定义和扩展

### 添加新敌机类型
```javascript
// 在createEnemyAircraft函数中添加不同的敌机模型
function createBossEnemy() {
    // 创建更大、更强的敌机
}
```

### 添加新武器
```javascript
// 在shoot函数中添加不同的武器类型
function shootMissile() {
    // 发射导弹
}
```

### 添加道具系统
```javascript
// 创建可收集的道具
function createPowerUp() {
    // 生命恢复、武器升级等
}
```

## 🌟 游戏截图

游戏包含以下视觉元素：
- 蓝色玩家飞机（可控制）
- 红色敌机（AI控制）
- 黄色玩家子弹
- 红色敌机子弹
- 绿色地面
- 白色云朵
- 动态爆炸效果

## 🔧 浏览器兼容性

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

需要支持WebGL的现代浏览器。

## 📈 性能建议

- 建议在独立显卡的设备上运行以获得最佳性能
- 如果帧率较低，可以降低阴影质量或关闭某些特效
- 移动设备可能需要调整渲染质量

## 🤝 贡献

欢迎提交问题和改进建议！可以扩展的功能：
- 多人游戏模式
- 更多武器类型
- 关卡系统
- 音效和背景音乐
- 更复杂的敌机AI
- 地形变化

## 📄 许可证

MIT License - 可自由使用和修改

---

基于 Three.js r178 开发 | 2025年最新版本
