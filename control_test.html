<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制方向测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            font-family: 'Arial', sans-serif;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.7);
            padding: 20px;
            border-radius: 10px;
            max-width: 400px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
        }
        
        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
        }
        
        .key-pressed {
            color: #00ff00;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="info">
        <h3>🎮 控制方向测试</h3>
        <div>这个页面用于测试WASD控制方向是否正确</div>
        <div><strong>预期行为:</strong></div>
        <div>• W键 - 飞机机头向上抬起，机翼保持水平</div>
        <div>• S键 - 飞机机头向下俯冲，机翼保持水平</div>
        <div>• A键 - 飞机应该向左转</div>
        <div>• D键 - 飞机应该向右转</div>
        <div><br><strong>观察要点:</strong></div>
        <div>• 飞机的蓝色机头指向应该与按键方向一致</div>
        <div>• W/S键时机翼应该始终保持水平状态</div>
        <div>• 相机视角应该跟随飞机朝向</div>
    </div>
    
    <div id="controls">
        <div><strong>🎮 测试控制:</strong></div>
        <div id="key-w">W - 拉升 (机头向上)</div>
        <div id="key-s">S - 俯冲 (机头向下)</div>
        <div id="key-a">A - 左转</div>
        <div id="key-d">D - 右转</div>
    </div>
    
    <div id="status">
        <div>飞机状态:</div>
        <div>俯仰角: <span id="pitch">0°</span></div>
        <div>偏航角: <span id="yaw">0°</span></div>
        <div>当前按键: <span id="current-key">无</span></div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script>
        let scene, camera, renderer;
        let playerAircraft;
        let keys = {};

        // 初始化场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x87CEEB, 10, 100);

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(-8, 3, 0);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x87CEEB);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);

            // 设置光照
            setupLighting();

            // 创建简单环境
            createEnvironment();

            // 创建飞机
            createPlayerAircraft();

            // 设置事件监听器
            setupEventListeners();

            // 开始渲染循环
            animate();
        }

        // 设置光照
        function setupLighting() {
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(50, 50, 25);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
        }

        // 创建环境
        function createEnvironment() {
            // 地面
            const groundGeometry = new THREE.PlaneGeometry(100, 100);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -10;
            ground.receiveShadow = true;
            scene.add(ground);

            // 添加坐标轴辅助线
            const axesHelper = new THREE.AxesHelper(10);
            scene.add(axesHelper);

            // 添加网格辅助线
            const gridHelper = new THREE.GridHelper(50, 50);
            gridHelper.position.y = -10;
            scene.add(gridHelper);
        }

        // 创建玩家飞机
        function createPlayerAircraft() {
            const aircraftGroup = new THREE.Group();
            
            // 机身 - 蓝色锥形，尖头朝前
            const fuselageGeometry = new THREE.ConeGeometry(0.4, 5, 8);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = -Math.PI / 2; // 尖头朝向X轴正方向
            fuselage.position.x = 0.5;
            fuselage.castShadow = true;
            aircraftGroup.add(fuselage);
            
            // 驾驶舱
            const cockpitGeometry = new THREE.SphereGeometry(0.3, 8, 6);
            const cockpitMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x87CEEB, 
                transparent: true, 
                opacity: 0.7 
            });
            const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
            cockpit.position.set(0.8, 0.1, 0);
            cockpit.scale.set(1, 0.6, 0.8);
            cockpit.castShadow = true;
            aircraftGroup.add(cockpit);
            
            // 机翼
            const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
            const mainWingGeometry = new THREE.BoxGeometry(0.3, 0.1, 4);
            const leftWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
            leftWing.position.set(0, 0, 2);
            leftWing.castShadow = true;
            const rightWing = new THREE.Mesh(mainWingGeometry, wingMaterial);
            rightWing.position.set(0, 0, -2);
            rightWing.castShadow = true;
            aircraftGroup.add(leftWing);
            aircraftGroup.add(rightWing);
            
            // 尾翼
            const verticalTailGeometry = new THREE.BoxGeometry(0.2, 1.5, 0.1);
            const verticalTail = new THREE.Mesh(verticalTailGeometry, wingMaterial);
            verticalTail.position.set(-2, 0.5, 0);
            verticalTail.castShadow = true;
            aircraftGroup.add(verticalTail);
            
            // 添加方向指示器（红色箭头指向机头方向）
            const arrowGeometry = new THREE.ConeGeometry(0.2, 1, 4);
            const arrowMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
            arrow.rotation.z = -Math.PI / 2;
            arrow.position.set(3, 0, 0);
            aircraftGroup.add(arrow);
            
            aircraftGroup.position.set(0, 0, 0);
            playerAircraft = aircraftGroup;
            scene.add(playerAircraft);
        }

        // 设置事件监听器
        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
                updateKeyDisplay();
                event.preventDefault();
            });
            
            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
                updateKeyDisplay();
            });
            
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        // 更新按键显示
        function updateKeyDisplay() {
            const keyElements = {
                'KeyW': document.getElementById('key-w'),
                'KeyS': document.getElementById('key-s'),
                'KeyA': document.getElementById('key-a'),
                'KeyD': document.getElementById('key-d')
            };

            let currentKeys = [];
            
            Object.keys(keyElements).forEach(key => {
                if (keys[key]) {
                    keyElements[key].classList.add('key-pressed');
                    currentKeys.push(key.replace('Key', ''));
                } else {
                    keyElements[key].classList.remove('key-pressed');
                }
            });

            document.getElementById('current-key').textContent = 
                currentKeys.length > 0 ? currentKeys.join(' + ') : '无';
        }

        // 更新飞机
        function updateAircraft() {
            if (!playerAircraft) return;

            const rotationSpeed = 0.02;
            
            // WASD控制飞机的俯仰和偏航 (保持机翼水平)
            if (keys['KeyW']) playerAircraft.rotation.z += rotationSpeed; // 拉升 (机头向上，机翼保持水平)
            if (keys['KeyS']) playerAircraft.rotation.z -= rotationSpeed; // 俯冲 (机头向下，机翼保持水平)
            if (keys['KeyA']) playerAircraft.rotation.y += rotationSpeed; // 左转
            if (keys['KeyD']) playerAircraft.rotation.y -= rotationSpeed; // 右转
            
            // 限制俯仰角度 (现在使用Z轴)
            playerAircraft.rotation.z = Math.max(-Math.PI/2, Math.min(Math.PI/2, playerAircraft.rotation.z));
            
            // 更新相机跟随
            updateCamera();
            
            // 更新状态显示
            updateStatus();
        }

        // 更新相机
        function updateCamera() {
            const cameraDistance = 8;
            const cameraHeight = 3;
            
            // 计算飞机朝向
            const aircraftDirection = new THREE.Vector3(1, 0, 0);
            aircraftDirection.applyEuler(playerAircraft.rotation);
            
            // 计算相机位置
            const cameraOffset = aircraftDirection.clone().multiplyScalar(-cameraDistance);
            const targetCameraPosition = playerAircraft.position.clone().add(cameraOffset);
            targetCameraPosition.y += cameraHeight;
            
            // 平滑移动相机
            camera.position.lerp(targetCameraPosition, 0.1);
            
            // 瞄准飞机前方
            const lookAtTarget = playerAircraft.position.clone().add(aircraftDirection.multiplyScalar(5));
            camera.lookAt(lookAtTarget);
        }

        // 更新状态显示
        function updateStatus() {
            const pitch = (playerAircraft.rotation.z * 180 / Math.PI).toFixed(1); // 现在使用Z轴
            const yaw = (playerAircraft.rotation.y * 180 / Math.PI).toFixed(1);

            document.getElementById('pitch').textContent = pitch + '°';
            document.getElementById('yaw').textContent = yaw + '°';
        }

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            
            updateAircraft();
            
            renderer.render(scene, camera);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
