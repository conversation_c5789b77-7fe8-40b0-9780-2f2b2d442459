#!/usr/bin/env python3
"""
简单的HTTP服务器启动脚本
用于运行Three.js空战游戏
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def start_server():
    """启动本地HTTP服务器"""
    
    # 设置端口
    PORT = 8000
    
    # 检查文件是否存在
    required_files = ['index.html', 'game.js']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有游戏文件都在当前目录中。")
        return
    
    # 创建服务器
    Handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print("🚀 Three.js 空战游戏服务器启动中...")
            print(f"📡 服务器地址: http://localhost:{PORT}")
            print(f"📁 服务目录: {os.getcwd()}")
            print("\n🎮 游戏控制:")
            print("   WASD - 移动飞机")
            print("   鼠标 - 瞄准")
            print("   左键/空格 - 射击")
            print("   R - 重新装弹")
            print("\n按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 已自动打开浏览器")
            except:
                print("⚠️  请手动在浏览器中打开: http://localhost:8000")
            
            print("-" * 50)
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        print("感谢游玩 Three.js 空战游戏！")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print("请尝试以下解决方案:")
            print("1. 关闭其他使用该端口的程序")
            print("2. 等待几分钟后重试")
            print("3. 重启计算机")
        else:
            print(f"❌ 启动服务器时出错: {e}")

def show_help():
    """显示帮助信息"""
    print("Three.js 空战游戏 - 服务器启动工具")
    print("=" * 40)
    print("\n使用方法:")
    print("  python start_server.py        # 启动游戏服务器")
    print("  python start_server.py -h     # 显示帮助信息")
    print("\n系统要求:")
    print("  - Python 3.6+")
    print("  - 现代浏览器 (Chrome, Firefox, Safari, Edge)")
    print("  - 支持WebGL的显卡")
    print("\n游戏文件:")
    print("  - index.html  (主页面)")
    print("  - game.js     (游戏逻辑)")
    print("  - README.md   (说明文档)")
    print("\n技术特性:")
    print("  - 基于 Three.js r178")
    print("  - 3D飞行射击游戏")
    print("  - 实时光影效果")
    print("  - 粒子爆炸特效")

if __name__ == "__main__":
    print("🎮 Three.js 空战游戏")
    print("=" * 30)
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        start_server()
